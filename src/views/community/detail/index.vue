<template>
  <div id="zonePage">
    <div class="loading-overlay" v-if="viewLoading">
      <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="backList">麒麟社区</span>
        <span class="title"> / </span>
        <span class="current">帖子详情</span>
      </div>
      <div @click="back" style="cursor: pointer; color: #2e7fff">返回</div>
    </div>

    <div style="margin-top: 50px">
      <div class="contentCard">
        <div class="top_card">
          <div class="card_content">
            <div class="flex align-center just-sb">
              <div class="text">帖子详情</div>
              <div>
                <div
                  style="display: inline-block"
                  v-if="
                    userInfo.roleKeyList.includes('auditManager') ||
                    userInfo.roleKeyList.includes('sysAdmin')
                  "
                >
                  <a-button
                    type="primary"
                    style="
                      background: linear-gradient(
                        270deg,
                        #0142fd 0%,
                        #2475f9 100%
                      );
                      border-radius: 4px;
                      font-weight: 500;
                      border: none;
                    "
                    v-if="forumData.top !== 1"
                    @click="toTop(1)"
                  >
                    置顶
                  </a-button>
                  <a-button
                    type="primary"
                    style="
                      background: linear-gradient(
                        270deg,
                        #0142fd 0%,
                        #2475f9 100%
                      );
                      border-radius: 4px;
                      font-weight: 500;
                      border: none;
                    "
                    v-else
                    @click="toTop(2)"
                  >
                    取消置顶
                  </a-button>
                </div>
                <!-- <a-button
                  v-if="userInfo.id == forumData.createBy"
                  type="primary"
                  style="
                    background: rgba(245, 29, 15, 0.1);
                    border-radius: 4px;
                    font-weight: 500;
                    border: none;
                    color: #f51d0f;
                    margin-left: 16px;
                  "
                  @click="showDelete"
                >
                  删除
                </a-button> -->
                <a-button
                  v-if="
                    forumData.status == '-1' &&
                    userInfo.id == forumData.createBy
                  "
                  type="primary"
                  @click="handleSubmit"
                  style="
                    background: linear-gradient(
                      270deg,
                      #0142fd 0%,
                      #2475f9 100%
                    );
                    border-radius: 4px;
                    font-weight: 500;
                    border: none;
                    margin-left: 16px;
                  "
                  :loading="addLoading"
                >
                  提交
                </a-button>
                <a-button
                  @click="trackView"
                  v-if="
                    (forumData.type == 1 &&
                      userInfo.id == forumData.createBy) ||
                    (forumData.type == 1 &&
                      userInfo.roleKeyList.includes('sysAdmin')) ||
                    (forumData.type == 1 &&
                      userInfo.roleKeyList.includes('auditManager'))
                  "
                  type="primary"
                  style="
                    background: linear-gradient(
                      270deg,
                      #0142fd 0%,
                      #2475f9 100%
                    );
                    border-radius: 4px;
                    font-weight: 500;
                    border: none;
                    margin-left: 16px;
                  "
                >
                  流程跟踪
                </a-button>
              </div>
            </div>

            <div
              class="margin_t_20"
              v-if="
                forumData.status == '-1' && userInfo.id == forumData.createBy
              "
            >
              <a-row class="form_content">
                <a-col :span="2" class="label-content">
                  <span class="warning" style="color: red">*</span>
                  <div class="label-word" style="letter-spacing: 27px">
                    主题
                  </div>
                </a-col>
                <a-col :span="9" class="input-content">
                  <a-input
                    v-model:value="forumData.title"
                    placeholder="请输入主题（20字以内）"
                    :maxlength="20"
                  />
                </a-col>
                <a-col
                  :span="2"
                  class="label-content"
                  style="margin-left: 93px"
                >
                  <span class="warning" style="color: red">*</span>
                  <div class="label-word" style="letter-spacing: 27px">类型</div>
                </a-col>
                <a-col :span="9" class="input-content">
                  <a-input
                    :value="dealType(forumData.type)"
                    :maxlength="20"
                    disabled
                  />
                </a-col>
              </a-row>

              <a-row class="form_content" style="margin-top: 8px">
                <a-col
                  :span="2"
                  class="label-content"
                  style="align-items: flex-start; padding-top: 4px"
                >
                  <span class="warning" style="color: red">*</span>
                  <div class="label-word" style="letter-spacing: 27px">
                    内容
                  </div>
                </a-col>
                <a-col :span="22" class="input-content">
                  <markDown
                    :valueHtml="forumData.content"
                    @valueHtml-change="changeRich"
                  ></markDown>
                </a-col>
              </a-row>

              <a-row class="form_content" style="margin-top: 8px">
                <a-col :span="2" class="label-content">
                  <span class="warning" style="opacity: 0">*</span>
                  <div class="label-word" style="letter-spacing: 27px">
                    附件
                  </div>
                </a-col>
                <a-col
                  :span="22"
                  class="input-content"
                  style="align-items: center"
                >
                  <a-upload
                    v-model:file-list="forumData.file"
                    :customRequest="uploadFile"
                    :multiple="false"
                    :max-count="1"
                    accept=".doc,.docx,.xls,.xlsx,.pdf,.jpg,.jpeg,.png,.mp4,.mov,.avi"
                    :show-upload-list="false"
                  >
                    <a-button
                      v-if="ShowBtn"
                      style="
                        background: linear-gradient(
                          270deg,
                          #0142fd 0%,
                          #2475f9 100%
                        );
                        border-radius: 4px 4px 4px 4px;
                        font-weight: 500;
                        font-size: 14px;
                        color: #ffffff;
                        line-height: 20px;
                        padding: 6px 16px;
                      "
                      :loading="uploading"
                    >
                      上传附件
                    </a-button>
                    <template v-if="!ShowBtn">
                      <div class="custom-upload-item">
                        <span
                          class="file-name"
                          v-if="hasFile"
                          style="color: #0c70eb"
                        >
                          {{ displayFileName }}
                        </span>
                        <delete-outlined
                          v-if="hasFile"
                          @click.stop="handleRemove"
                        />
                      </div>
                    </template>
                  </a-upload>
                  <div
                    v-if="!hasFile"
                    style="
                      font-size: 14px;
                      color: rgba(0, 0, 0, 0.25);
                      margin-left: 14px;
                    "
                  >
                    注：视频限制20M以内
                  </div>
                </a-col>
              </a-row>
              <a-row class="form_content">
                <a-col :span="2" class="label-content">
                  <span class="warning" style="color: red">*</span>
                  <div class="label-word">是否匿名</div>
                </a-col>
                <a-col :span="22" class="input-content">
                  <a-radio-group
                    v-model:value="forumData.anonymity"
                  >
                    <a-radio :value="1">是</a-radio>
                    <a-radio :value="0">否</a-radio>
                  </a-radio-group>
                </a-col>
              </a-row>
            </div>
            <div class="margin_t_20" v-else>
              <div class="card_table">
                <div class="table_left">
                  <div class="margin_t_12" style="letter-spacing: 27px">主题</div>
                </div>
                <div class="table_right">
                  <div class="margin_t_12">{{ forumData.title }}</div>
                </div>
                <div class="table_left">
                  <div class="margin_t_12">发布日期</div>
                </div>
                <div class="table_right">
                  <div class="margin_t_12">{{ forumData.createTime }}</div>
                </div>
              </div>
              <div class="card_table">
                <div class="table_left">
                  <div class="margin_t_12" style="letter-spacing: 7px">提交人</div>
                </div>
                <div class="table_right">
                  <div class="margin_t_12" v-if="forumData.anonymity == 0">
                    <span style="margin-right: 4px">
                      {{ forumData.createName }}
                    </span>
                    <span v-if="forumData.createName != '系统管理员'">
                      {{ forumData.orgPathName }}
                    </span>
                  </div>
                  <div v-if="forumData.anonymity == 1">
                    <div class="margin_t_12">匿名</div>
                  </div>
                </div>
                <div class="table_left">
                  <div class="margin_t_12">提交类型</div>
                </div>
                <div class="table_right">
                  <div class="margin_t_12">{{ dealType(forumData.type) }}</div>
                </div>
              </div>
              <div class="card_table">
                <div class="table_left">
                  <div class="margin_t_12" style="letter-spacing: 27px">内容</div>
                </div>
                <div class="table_right htmlContentImg" @click="handleImgClick">
                  <div
                    class="margin_t_12 margin_b_12"
                    style="white-space: pre-wrap"
                    v-html="forumData.content"
                  ></div>
                </div>
              </div>
              <div class="card_table">
                <div class="table_left table_line">
                  <div class="margin_t_12" style="letter-spacing: 27px">附件</div>
                </div>
                <div class="table_right table_line">
                  <div
                    class="margin_t_12 tableFile"
                    v-if="forumData.file && forumData.file.length > 0"
                    @click="previewFile(forumData.file[0])"
                  >
                    {{ forumData.file[0].fileName }}
                  </div>
                  <div class="margin_t_12" v-else>暂无</div>
                </div>
              </div>
            </div>
            <idea-list
              @feed-back="feedBack"
              @allot-view="allotView"
              @unsolved="unsolved"
              @preview-file="previewFile"
              :dataDetail="forumData"
              v-if="forumData.type == 1"
            />
          </div>
        </div>

        <div class="leaveWord">
          <div class="leaveContent">
            <review-list :id="forumData.id" @refresh-page="refreshPage" />
          </div>
        </div>

        <div class="leaveWord">
          <div class="leaveContent">
            <attach-list :detailData="forumData" @refresh-page="refreshPage" />
          </div>
        </div>
      </div>
    </div>
    <!-- 分配 -->
    <a-modal
      :visible="allotVisible"
      @cancel="closeModal"
      width="1200px"
      :footer="null"
    >
      <allot-model
        @reject-view="rejectView"
        v-if="allotVisible"
        @modal-close="closeModal"
        @preview-file="previewFile"
        :dataDetail="forumData"
        @refresh="getData"
      />
    </a-modal>
    <!-- 情况反馈 -->
    <a-modal
      :visible="backVisible"
      @cancel="backModal"
      width="1200px"
      :footer="null"
    >
      <couple-list
        :typeObject="typeObject"
        :id="forumData.id"
        @decline-view="declinView"
        @cancel-idea="cancelIdea"
        @refresh-view="getData"
        v-if="backVisible"
      />
    </a-modal>
    <!-- 驳回 -->
    <a-modal
      :visible="refuseVisible"
      @cancel="closeRefuse"
      width="680px"
      :footer="null"
    >
      <refuse-model
        @cancel-view="closeRefuse"
        v-if="refuseVisible"
        :id="forumData.id"
      />
    </a-modal>
    <!-- 拒绝 -->
    <a-modal
      :visible="rejectVisible"
      @cancel="closeReject"
      width="680px"
      :footer="null"
    >
      <reject-model
        @close-view="closeView"
        :id="forumData.id"
        v-if="rejectVisible"
      />
    </a-modal>
    <!-- 流程跟踪 -->
    <a-modal
      :visible="trackVisible"
      @cancel="closeTrack"
      width="590px"
      :footer="null"
    >
      <track-list v-if="trackVisible" :history="forumData.processList" />
    </a-modal>
    <a-modal
      v-model:visible="deleteShow"
      :destroyOnClose="true"
      width="500px"
      @ok="deleteCard"
      @cancel="!deleteShow"
    >
      是否删除该帖？
    </a-modal>
    <a-modal
      :visible="previewVisible"
      centered
      :footer="null"
      @cancel="handleCancel"
    >
      <video
        v-if="isVideo"
        controls
        style="width: 100%; height: 100%; margin-top: 20px"
        :src="videoUrl"
      />
      <a-image
        v-if="isImage"
        style="width: 100%; height: 100%; margin-top: 20px"
        :src="imgUrl"
        alt=""
      />
    </a-modal>
    <a-image
      v-if="visibleImg"
      :width="200"
      :src="imageUrl"
      :preview="{
        visible: previewVisibleImg,
        onVisibleChange: handleVisibleChangeImg,
      }"
      @click="closePreviewImg"
    />
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  computed,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import markDown from "@/components/markDown/index.vue";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import reviewList from "./components/reviewList.vue";
import attachList from "./components/attachList.vue";
import allotModel from "./components/allotModel.vue";
import ideaList from "./components/ideaList.vue";
import refuseModel from "./components/refuseModel.vue";
import rejectModel from "./components/rejectModel.vue";
import coupleList from "./components/coupleList.vue";
import trackList from "./components/trackList.vue";
import { DeleteOutlined } from "@ant-design/icons-vue";
import {
  getDetail,
  setTop,
  setDelete,
  refuseTop,
  setConfirm,
  rsetMessage,
} from "@/api/community/index.js";
import { message } from "ant-design-vue";
import eventBus from "@/utils/eventBus";
export default defineComponent({
  components: {
    reviewList,
    attachList,
    allotModel,
    ideaList,
    markDown,
    refuseModel,
    coupleList,
    trackList,
    rejectModel,
    DeleteOutlined,
  },
  setup() {
    const Route = useRoute();
    const Router = useRouter();
    const data = reactive({
      viewLoading: false,
      uploading: false,
      addLoading: false,
      ShowBtn: false,
      deleteShow: false,
      refuseVisible: false,
      previewVisible: false,
      isVideo: false,
      isImage: false,
      videoUrl: "",
      imgUrl: "",
      allotVisible: false,
      backVisible: false,
      rejectVisible: false,
      trackVisible: false,
      typeObject: {},
      forumData: {},
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
      options: [
        { name: "需求建议", id: 1 },
        { name: "官方公告", id: 2 },
        // { name: "技术交流", id: 3 },
        // { name: "经验分享", id: 4 },
        // { name: "问答互勉", id: 5 },
      ],
      backedId: "",
      imageUrl: "",
      visibleImg: false,
      previewVisibleImg: false,
    });
    const getData = () => {
      getDetail(Route.query.id).then(async (res) => {
        data.forumData = res.data;
        const ImageObject = await findConsecutiveImages(data.forumData.content);
        await Promise.all(ImageObject.consecutiveGroups.map(item=>{
          return Promise.all(item.map(async imgItem => {
            const size = await getImageSize(imgItem.src);
            console.log(size.width, size.height);
            imgItem.width = size.width;
            imgItem.height = size.height;
          }));
        }));
        ImageObject.consecutiveGroups.forEach(group => {
          if(group.length === 1 ){
            processSingleImage(group[0]);
          } else if(group.length > 1){
            processMultipleImages(group);
          } else {
          }
        });
        // 更新HTML内容中的图片尺寸和布局
        if (ImageObject.consecutiveGroups.length > 0) {
          const parser = new DOMParser();
          const doc = parser.parseFromString(data.forumData.content, 'text/html');

          // 处理每个连续图片组
          ImageObject.consecutiveGroups.forEach(group => {
            if (group.length > 1) {
              // 多张图片需要创建容器并排显示
              const firstImg = doc.querySelector(`img[src="${group[0].src}"]`);
              if (firstImg) {
                // 创建图片容器
                const container = doc.createElement('div');
                container.style.cssText = 'display: flex; gap: 24px; align-items: flex-start; margin: 10px 0; flex-wrap: wrap;';

                // 将所有连续图片移动到容器中
                group.forEach(imgItem => {
                  const img = doc.querySelector(`img[src="${imgItem.src}"]`);
                  if (img) {
                    // 设置图片尺寸
                    if (imgItem.showWidth && imgItem.showHeight) {
                      img.width = imgItem.showWidth;
                      img.height = imgItem.showHeight;
                    }
                    // 移除原有的style属性中的宽高设置
                    if (img.style) {
                      img.style.width = '';
                      img.style.height = '';
                      img.style.display = 'block';
                      img.style.margin = '0';
                    }
                    // 将图片移动到容器中
                    container.appendChild(img.cloneNode(true));
                    img.remove();
                  }
                });

                // 将容器插入到第一张图片的位置
                if (firstImg.parentNode) {
                  firstImg.parentNode.insertBefore(container, firstImg);
                }
              }
            } else if (group.length === 1) {
              // 单张图片处理
              const img = doc.querySelector(`img[src="${group[0].src}"]`);
              if (img && group[0].showWidth && group[0].showHeight) {
                img.width = group[0].showWidth;
                img.height = group[0].showHeight;
                // 移除style属性中的宽高设置
                if (img.style) {
                  img.style.width = '';
                  img.style.height = '';
                  img.style.display = 'block';
                  img.style.margin = '10px auto';
                  // 如果style属性为空，则移除整个style属性
                  if (img.style.cssText === 'display: block; margin: 10px auto;') {
                    img.style.cssText = 'display: block; margin: 10px auto;';
                  }
                }
              }
            }
          });

          // 更新forumData.content
          data.forumData.content = doc.body.innerHTML;
        }
        console.log('ImageObject',ImageObject)
        data.backedId = res.data.file?.id;
        data.forumData.processList = res.data.processList.reverse();
        data.forumData.file = res.data.file && res.data.file.fileName ? [res.data.file] : [];
        if (data.forumData.file && data.forumData.file.length == 0) {
          data.ShowBtn = true;
        }
      });
    };
    const refreshPage = () => {
      getData();
    };
    const previewFile = async (e) => {
      try {
        const { fileUrl: href, fileName: downName } = e;
        const token = localStorage.getItem("token") || "";
        const fileExtension = downName.split(".").pop().toLowerCase();
        const isImage = ["jpg", "jpeg", "png"].includes(fileExtension);
        const isVideo = ["mp4", "mov", "avi", "wmv", "mkv", "flv"].includes(
          fileExtension
        );
        if (isVideo) {
          downloadImage(e, 1);
        } else if (isImage) {
          openPreviewImg(e);
          return false;
        } else {
          let downloadUrl = new URL(href, window.location.origin).href;
          if (href.includes(window.location.origin)) {
            downloadUrl = href.replace(window.location.origin, "/portal");
          }
          const urlObj = new URL(downloadUrl);
          urlObj.searchParams.set("token", token);
          downloadUrl = urlObj.toString();
          window.open(downloadUrl, "_blank");
        }
      } catch (error) {
        console.error("文件处理失败:", error);
      }
      return false;
    };
    const downloadImage = (file, type) => {
      if (type == 1) {
        data.isImage = false;
        data.isVideo = true;
        const href = file.fileUrl;
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = href;
        if (href.includes(windowOrigin)) {
          newHref = "/portal" + href.split(windowOrigin)[1];
        }
        data.videoUrl = windowOrigin + newHref + "?token=" + token;
      } else if (type == 2) {
        data.isImage = true;
        data.isVideo = false;
        data.imgUrl = file.fileUrl;
      }
      data.previewVisible = true;
    };
    const handleCancel = () => {
      data.previewVisible = false;
    };
    const unsolved = (val) => {
      data.typeObject = val;
      data.backVisible = true;
    };

    const back = () => {
      Router.back(-1);
    };

    const allotView = () => {
      data.allotVisible = true;
    };

    const closeModal = () => {
      data.allotVisible = false;
    };

    const closeRefuse = () => {
      data.refuseVisible = false;
    };

    const backModal = () => {
      data.backVisible = false;
    };

    const rejectView = () => {
      data.refuseVisible = true;
      data.allotVisible = false;
    };

    const declinView = () => {
      data.rejectVisible = true;
      data.backVisible = false;
    };

    const feedBack = (val) => {
      data.typeObject = val;
      data.backVisible = true;
    };

    const closeReject = () => {
      data.rejectVisible = false;
    };

    const cancelIdea = () => {
      data.backVisible = false;
    };

    const closeView = () => {
      data.rejectVisible = false;
    };
    const closeTrack = () => {
      data.trackVisible = false;
    };

    const trackView = () => {
      data.trackVisible = true;
    };
    onMounted(() => {
      getData();
    });
    const dealType = (v) => {
      switch (v) {
        case 1:
          return "需求建议";
        case 2:
          return "官方公告";
        // case 3:
        //   return "技术交流";
        // case 4:
        //   return "经验分享";
        // case 5:
        //   return "问答互勉";
        default:
          return v;
      }
    };
    // 置顶
    const toTop = async (v) => {
      try {
        const response =
          v === 1
            ? await setTop(data.forumData.id)
            : await refuseTop(data.forumData.id);

        if (response.code === 200) {
          const action = v === 1 ? "置顶" : "取消置顶";
          message.success(`${action}成功`);
          eventBus.emit("communityTable");
          data.forumData.top = v === 1 ? "1" : null;
        }
      } catch (error) {
        message.error("操作失败，请重试");
      } finally {
        getData();
      }
    };
    const deleteCard = () => {
      setDelete(data.forumData.id).then((res) => {
        if (res.code == 200) {
          message.warning("删除成功");
          eventBus.emit("communityTable");
          Router.back(-1);
        }
      });
    };
    const uploadFile = async (info) => {
      const file = info.file;
      const isVideo = file.type.startsWith("video/");
      const maxVideoSize = 20 * 1024 * 1024;

      if (isVideo && file.size > maxVideoSize) {
        message.error("视频文件大小不能超过20MB");
        info.onError();
        return;
      }
      data.uploading = true;
      data.ShowBtn = true;
      try {
        let formData = new FormData();
        formData.append("file", file);
        const result = await uploadFileList(formData);
        if (result.code === 200) {
          if (!data.forumData.file) {
            data.forumData.file = [{}];
          }
          data.forumData.file[0] = {
            fileName: result.data.fileName,
            filePath: result.data.filePath,
            fileUrl: result.data.fileUrl,
          };
          data.ShowBtn = false;
          message.success("上传成功");
          info.onSuccess();
          return result;
        } else {
          message.error("上传失败");
          data.ShowBtn = true;
        }
      } catch (error) {
        message.error(error.message || "上传出错");
        info.onError();
        data.ShowBtn = true;
      } finally {
        data.uploading = false;
      }
    };
    const normalizedFileList = computed(() => {
      if (!data.forumData.file) return [];
      return Array.isArray(data.forumData.file)
        ? data.forumData.file
        : [data.forumData.file];
    });

    const hasFile = computed(() => {
      return (
        data.forumData.file &&
        (Array.isArray(data.forumData.file)
          ? data.forumData.file.length > 0
          : Object.keys(data.forumData.file).length > 0)
      );
    });

    const displayFileName = computed(() => {
      if (!data.forumData.file) return "";
      if (Array.isArray(data.forumData.file)) {
        return data.forumData.file[0]?.fileName || "";
      }
      return data.forumData.file[0].fileName || "";
    });

    const handleRemove = () => {
      data.forumData.file = Array.isArray(data.forumData.file) ? [] : {};
      data.ShowBtn = true;
    };
    const textWith = (value) => {
      if (!value) return false;
      let text = value.replace(/<[^>]+>/g, '');
      text = text.replace(/&nbsp;/gi, ' ');
      text = text.replace(/\s+/g, '');
      return text.length > 0;
    }
    const handleSubmit = () => {
      if (data.uploading == true) {
        message.error("附件上传中");
        return;
      }
      data.addLoading = true;
      if (!data.forumData.title) {
        message.error("请填写主题！");
        data.addLoading = false;
        return;
      }
      if (data.forumData.content === "<p><br></p>") {
        message.error("请填写内容！");
        data.addLoading = false;
        return;
      }
      let textVal = textWith(data.forumData.content);
      if(!textVal) {
        message.error("请输入文本！");
        data.addLoading = false;
        return;
      }
      if (data.forumData.anonymity === undefined) {
        message.error("请选择是否匿名发布！");
        data.addLoading = false;
        return;
      }
      const submitData = {
        id: data.forumData.id,
        title: data.forumData.title,
        type: data.forumData.type,
        content: data.forumData.content,
        anonymity: data.forumData.anonymity,
        ...(data.forumData.file[0]?.fileName && {
          file: {
            id: data.backedId,
            fileName: data.forumData.file[0].fileName,
            filePath: data.forumData.file[0].filePath,
            fileUrl: data.forumData.file[0].fileUrl,
          },
        }),
      };
      rsetMessage(submitData).then((res) => {
        if (res.code == 200) {
          message.success("发帖成功");
          getData();
          eventBus.emit("communityTable");
          Router.back(-1);
          data.addLoading = false;
        } else {
          data.addLoading = false;
        }
      });
    };
    const showDelete = () => {
      data.deleteShow = true;
    };

    const backList = () => {
      Router.push({
        name: "communityHome",
      });
    };

    const openPreviewImg = (e) => {
      data.imageUrl = e.fileUrl;
      data.visibleImg = true;
      data.previewVisibleImg = true;
    };
    // 关闭预览
    const closePreviewImg = () => {
      data.imageUrl = "";
      data.visibleImg = false;
      data.previewVisibleImg = false;
    };
    // 处理预览状态变化
    const handleVisibleChangeImg = (visible) => {
      data.previewVisibleImg = visible;
      if (!visible) {
        data.imageUrl = "";
        data.visibleImg = false;
      }
    };

    const changeRich = (content) => {
      data.forumData.content = content;
    };

    const handleImgClick = (e) => {
      if (e.target.tagName === "IMG") {
        data.imageUrl = e.target.src;
        data.visibleImg = true;
        data.previewVisibleImg = true;
      }
    };
    // 单张图片处理
    function processSingleImage(image) {
      if (image.width >= 800) {
        // 大图：等比例缩放到1038PX
        const scale = 1038 / image.width;
        image.showWidth = 1038;
        image.showHeight = Math.trunc(scale * image.height);
      } else if (image.width >= 390) {
        // 中图：放大1.3倍
        image.showWidth = Math.trunc(1.3 * image.width);
        image.showHeight = Math.trunc(1.3 * image.height);
      } else if (image.width >= 200) {
        // 小图：放大1.3倍
        image.showWidth = Math.trunc(1.3 * image.width);
        image.showHeight = Math.trunc(1.3 * image.height);
      }
      // 校验宽度不超过1038
      if (image.showWidth > 1038) {
        const scale = 1038 / image.showWidth;
        image.showWidth = 1038;
        image.showHeight = Math.trunc(image.showHeight * scale);
      }
    }
    // 多张图片处理
    function processMultipleImages(images) {
      const rows = [];
      let currentRow = [];
      let currentRowWidth = 0;

      for (let i = 0; i < images.length; i++) {
        const image = images[i];

        // 大图单独占一行
        if (image.width >= 800) {
          // 先处理当前行（如果有图片）
          if (currentRow.length > 0) {
            processRow(currentRow, currentRowWidth);
            rows.push([...currentRow]);
            currentRow = [];
            currentRowWidth = 0;
          }

          // 大图等比例缩放到1038
          const scale = 1038 / image.width;
          image.showWidth = 1038;
          image.showHeight = Math.trunc(scale * image.height);
          rows.push([image]);
          continue;
        }

        // 中图/小图处理：先按1.3倍放大
        let showWidth, showHeight;
        if (image.width >= 390) {
          // 中图：放大1.3倍
          showWidth = Math.trunc(1.3 * image.width);
          showHeight = Math.trunc(1.3 * image.height);
        } else if (image.width >= 200) {
          // 小图：放大1.3倍
          showWidth = Math.trunc(1.3 * image.width);
          showHeight = Math.trunc(1.3 * image.height);
        } else {
          // 小于200px的图片保持原尺寸
          showWidth = image.width;
          showHeight = image.height;
        }

        // 检查是否能与当前行的图片并排
        const spacing = currentRow.length > 0 ? 24 : 0; // 图片间距
        const totalWidth = currentRowWidth + showWidth + spacing;

        if (totalWidth <= 1038) {
          // 可以放在当前行
          image.showWidth = showWidth;
          image.showHeight = showHeight;
          currentRow.push(image);
          currentRowWidth = totalWidth;
        } else {
          // 需要换行
          if (currentRow.length > 0) {
            processRow(currentRow, currentRowWidth);
            rows.push([...currentRow]);
          }

          // 开始新行
          image.showWidth = showWidth;
          image.showHeight = showHeight;
          currentRow = [image];
          currentRowWidth = showWidth;
        }
      }

      // 处理最后一行
      if (currentRow.length > 0) {
        processRow(currentRow, currentRowWidth);
        rows.push(currentRow);
      }

      return rows;
    }
    // 处理每一行的图片尺寸调整
    function processRow(row, totalWidth) {
      if (row.length === 1) {
        // 单张图片的行，检查是否需要缩放
        const image = row[0];
        if (image.showWidth > 1038) {
          const scale = 1038 / image.showWidth;
          image.showWidth = 1038;
          image.showHeight = Math.trunc(image.showHeight * scale);
        }
      } else if (row.length > 1) {
        // 多张图片并排处理

        // 如果总宽度超过1038，等比例缩放整行到1038
        if (totalWidth > 1038) {
          const spacing = (row.length - 1) * 24;
          const availableWidth = 1038 - spacing;
          const currentContentWidth = totalWidth - spacing;
          const scale = availableWidth / currentContentWidth;

          row.forEach(image => {
            image.showWidth = Math.trunc(image.showWidth * scale);
            image.showHeight = Math.trunc(image.showHeight * scale);
          });
        }

        // 高度对齐：找到行中最小高度，统一调整所有图片到相同高度
        const minHeight = Math.min(...row.map(img => img.showHeight));
        row.forEach(image => {
          if (image.showHeight > minHeight) {
            const scale = minHeight / image.showHeight;
            image.showWidth = Math.trunc(image.showWidth * scale);
            image.showHeight = minHeight;
          }
        });

        // 最终检查：如果调整后的总宽度仍然超过1038，再次等比例缩放
        const finalTotalWidth = row.reduce((sum, img) => sum + img.showWidth, 0) + (row.length - 1) * 24;
        if (finalTotalWidth > 1038) {
          const spacing = (row.length - 1) * 24;
          const availableWidth = 1038 - spacing;
          const currentContentWidth = finalTotalWidth - spacing;
          const finalScale = availableWidth / currentContentWidth;

          row.forEach(image => {
            image.showWidth = Math.trunc(image.showWidth * finalScale);
            image.showHeight = Math.trunc(image.showHeight * finalScale);
          });
        }
      }
    }
    const getImageSize = (url) => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve({ width: img.naturalWidth, height: img.naturalHeight });
        img.onerror = reject;
        img.src = url;
      });
    };
    const getAllNodes = (element) => {
      let nodes = [];
      for (let child of element.childNodes) {
        if (child.nodeType === Node.ELEMENT_NODE) {
          if (child.tagName === 'IMG') {
            nodes.push(child);
          } else {
            nodes.push(...getAllNodes(child));
          }
        } else if (child.nodeType === Node.TEXT_NODE && child.textContent.trim() !== '') {
          nodes.push(child);
        }
      }
      return nodes;
    }
    // 分析富文本中连续图片
    const findConsecutiveImages = async (richText) => {
      if (!richText.trim()) return { consecutiveGroups: [] };
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(richText, 'text/html');
        // 获取所有节点
        const allNodes = getAllNodes(doc.body);
        let consecutiveGroups = [];
        let currentGroup = [];
        for (let node of allNodes) {
          if (node.tagName === 'IMG') {
            currentGroup.push({
              src: node.src,
            });
          } else {
            // 遇到非图片节点，结束当前连续组
            if (currentGroup.length > 0) {
              consecutiveGroups.push([...currentGroup]);
              currentGroup = [];
            }
          }
        }
        // 处理最后一组
        if (currentGroup.length > 0) {
          consecutiveGroups.push(currentGroup);
        }
        return {
          consecutiveGroups: consecutiveGroups,
        };
      } catch (error) {
        return { consecutiveGroups: [] };
      }
    }
    return {
      ...toRefs(data),
      showDelete,
      backList,
      changeRich,
      handleImgClick,
      previewFile,
      handleRemove,
      refreshPage,
      handleSubmit,
      deleteCard,
      normalizedFileList,
      hasFile,
      displayFileName,
      uploadFile,
      toTop,
      dealType,
      allotView,
      trackView,
      handleCancel,
      closeView,
      closeTrack,
      feedBack,
      cancelIdea,
      unsolved,
      closeReject,
      backModal,
      declinView,
      closeRefuse,
      rejectView,
      closeModal,
      back,
      openPreviewImg,
      closePreviewImg,
      handleVisibleChangeImg,
      getData,
    };
  },
});
</script>

<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.htmlContentImg img {
  max-width: 100%;
  height: auto;
  cursor: pointer;
  display: block;
  margin: 0 auto;
}

.htmlContentImg div[style*="display: flex"] {
  max-width: 1038px;
  margin: 10px auto;
}

.htmlContentImg div[style*="display: flex"] img {
  margin: 0;
  flex-shrink: 0;
}
</style>