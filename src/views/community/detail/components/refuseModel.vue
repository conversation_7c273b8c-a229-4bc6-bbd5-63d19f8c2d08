<template>
  <div class="modelContent">
    <div class="title">
      <img
        src="@/assets/images/community/arrowRight.png"
        width="14px"
        height="8px"
        style="margin-right: 10px"
        alt=""
      />
      <img
        src="@/assets/images/community/refuse.png"
        width="72px"
        height="18px"
        alt=""
      />
    </div>
    <div class="margin_t_32">
      <a-textarea
        v-model:value="inputValue"
        placeholder="请输入驳回原因"
        :rows="4"
        showCount
        :maxlength="100"
      />
    </div>
    <div class="flex just-center align-center">
      <a-button
        @click="abolish"
        type="primary"
        style="
          background: rgba(1, 61, 253, 0.1);
          border-radius: 4px;
          font-weight: 500;
          border: none;
          margin-left: 16px;
          color: #0c70eb;
        "
      >
        取消
      </a-button>
      <a-button
        :loading="auditLoading"
        type="primary"
        @click="submit"
        style="
          background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
          border-radius: 4px;
          font-weight: 500;
          border: none;
          margin-left: 16px;
        "
      >
        提交
      </a-button>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs } from "vue";
import { message } from "ant-design-vue";
import { setAllocation } from "@/api/community/index.js";
import { useRouter } from "vue-router";
import eventBus from "@/utils/eventBus";
export default defineComponent({
  emits: ["cancel-view"],
  props:{
    id:{
      type: [String,Number],
      default: null,
    }
  },
  setup(props, { emit }) {
    const data = reactive({
      inputValue: "",
      auditLoading: false,
    });
    const Router = useRouter();
    const submit = () => {
      let params = {
        auditReason:data.inputValue,
        auditResult:0,
        id:props.id
      }
      if (!data.inputValue) return message.error("请输入驳回原因");
      setAllocation(params).then(res=>{
        if(res.code==200){
          message.success('驳回成功')
          eventBus.emit("communityTable");
          Router.push({
            name:'communityHome'
          })
        }
      })
    };

    const abolish = () => {
      emit("cancel-view");
    };

    return {
      ...toRefs(data),
      submit,
      abolish,
      Router
    };
  },
});
</script>
<style lang="scss" scoped>
.modelContent {
  margin: 0 16px;
}
.title {
  margin-top: -8px;
}
</style>